2025-07-22 13:59:04,313 - __main__ - INFO - Starting battery type generation...
2025-07-22 13:59:04,313 - __main__ - INFO - Loading data files...
2025-07-22 13:59:04,313 - __main__ - INFO - Loading battery lifecycle timelines...
2025-07-22 13:59:04,364 - __main__ - INFO - Loaded 28817 battery lifecycle records
2025-07-22 13:59:04,364 - __main__ - INFO - Loading HV repair data...
2025-07-22 13:59:04,384 - __main__ - INFO - Loaded 13083 HV repair records
2025-07-22 13:59:04,385 - __main__ - INFO - Loading vehicle battery assignment files...
2025-07-22 13:59:08,885 - __main__ - INFO - Loaded 26654 records from batteryid file
2025-07-22 13:59:08,885 - __main__ - INFO - Loaded 23256 records from zulassung file
2025-07-22 13:59:08,886 - __main__ - INFO - Cleaning data...
2025-07-22 13:59:08,958 - __main__ - INFO - Data cleaning completed
2025-07-22 13:59:08,958 - __main__ - INFO - Extracting unique batteries from lifecycle timelines...
2025-07-22 13:59:08,961 - __main__ - INFO - Found 18027 unique batteries in lifecycle timelines
2025-07-22 13:59:08,961 - __main__ - INFO - Building battery information from all sources...
2025-07-22 13:59:08,974 - __main__ - INFO - Processing HV repair data for battery types...
2025-07-22 13:59:09,540 - __main__ - INFO - Processing vehicle assignment files for battery types...
2025-07-22 13:59:09,540 - __main__ - INFO - Processing vehicle_assignment_with_id...
2025-07-22 13:59:10,628 - __main__ - INFO - Processing vehicle_assignment_with_zulassung...
2025-07-22 13:59:11,565 - __main__ - INFO - Battery type analysis complete:
2025-07-22 13:59:11,565 - __main__ - INFO -   Total batteries: 18027
2025-07-22 13:59:11,565 - __main__ - INFO -   Batteries with type: 18027
2025-07-22 13:59:11,566 - __main__ - INFO -   Batteries without type: 0
2025-07-22 13:59:11,566 - __main__ - INFO -   Sources breakdown: {'hv_repair': 11043, 'vehicle_assignment_with_id': 6984, 'vehicle_assignment_with_zulassung': 0}
2025-07-22 13:59:11,566 - __main__ - INFO - Generating battery type output...
2025-07-22 13:59:11,635 - __main__ - INFO - Saved battery type data to battery_type.csv
2025-07-22 13:59:11,635 - __main__ - INFO - Saved statistics to battery_type_analysis_statistics.txt
2025-07-22 13:59:11,637 - __main__ - INFO - Battery type generation completed successfully!
2025-07-22 13:59:11,637 - __main__ - INFO - Results saved to: battery_type.csv
2025-07-22 13:59:11,637 - __main__ - INFO - Statistics saved to: battery_type_analysis_statistics.txt
